#include "BETUtilities.h"
#include "LogDruids.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonReader.h"
#include "Serialization/JsonSerializer.h"

FString UBETUtilities::GetLookAtSubjectsAsYAML(ULookAtComponent* LookAtComponent)
{
	FString YAMLOutput("LookAtSubjects:");

	if (IsValid(LookAtComponent))
	{
		int NumTargets = LookAtComponent->GetNumTargets();
		for (int i = 0; i < NumTargets; i++)
		{
			FBlackEyeTarget BlackEyeTarget;
			LookAtComponent->GetTargetAtIndex(BlackEyeTarget, i);
			if (BlackEyeTarget.Actor.IsValid())
			{
				YAMLOutput += FString::Printf(TEXT("\n  -SlotIndex:%d\n    UseComponentBounds:%s\n    BoundingRadius:%f\n    Weight:%f\n    ActorName:%s\n    ComponentName:%s\n    BoneName:%s"),
					i + 1,
					(BlackEyeTarget.bAutoSize ? TEXT("true") : TEXT("false")),
					BlackEyeTarget.BoundingRadius,
					BlackEyeTarget.Weight,
					*BlackEyeTarget.Actor->GetActorNameOrLabel(),
					*BlackEyeTarget.ComponentName,
					*BlackEyeTarget.BoneName);
			}
		}
	}

	YAMLOutput += "\n";
	
	return YAMLOutput;
}

FString UBETUtilities::GetLookOptionsAsYAML(ULookAtComponent* LookAtComponent)
{
	FString YAMLOutput("LookOptions:");

	if (IsValid(LookAtComponent))
	{
		// Convert TargetResolveType enum to string
		FString TargetResolveTypeString;
		switch (LookAtComponent->TargetResolveType)
		{
			case VTRM_ScreenCenter:
				TargetResolveTypeString = TEXT("Screenspace Center");
				break;
			case VTRM_WorldCenter:
				TargetResolveTypeString = TEXT("Worldspace Center");
				break;
			default:
				TargetResolveTypeString = TEXT("Unknown");
				break;
		}

		YAMLOutput += FString::Printf(TEXT("\n  - RollRelativeToParent: %s\n    Roll: %f\n    CameraPlateDistance: %f\n    CameraPedestalHeight: %f\n    TargetResolveType: %s\n    VelocityLookAheadTime: %f\n    VelocityLookAheadDamping: %f\n    LockPositionRelativeToParent: %s\n    KeepTargetOnScreen: %s"),
			(LookAtComponent->bRollRelativeToParent ? TEXT("true") : TEXT("false")),
			LookAtComponent->Roll,
			LookAtComponent->CameraPlateDistance,
			LookAtComponent->CameraPedestalHeight,
			*TargetResolveTypeString,
			LookAtComponent->VelocityLookAheadTime,
			LookAtComponent->VelocityLookAheadDamping,
			(LookAtComponent->bLockPositionRelativeToParent ? TEXT("true") : TEXT("false")),
			(LookAtComponent->bKeepTargetOnScreen ? TEXT("true") : TEXT("false")));
	}

	YAMLOutput += "\n";

	return YAMLOutput;
}

FString UBETUtilities::GetCompositionOptionsAsYAML(ULookAtComponent* LookAtComponent)
{
	FString YAMLOutput("CompositionOptions:");

	/*
	UPROPERTY(EditAnywhere, Interp, Category = "Composition", DisplayName = "Subject Offset", meta = (Tooltip = "Adjusts the final look at point by this translation. Only possible when using `Worldspace Center` for subject resolve.", EditCondition = "TargetResolveType == EViewTargetResolveModes::VTRM_WorldCenter"))
	FVector TargetOffset;

	UPROPERTY(EditAnywhere, Interp, Category = "Composition", DisplayName = "Offset In Subject Local Space", meta = (Tooltip = "Selects whether or not the `Subject Offset` is relative to the local coordinate space of the subject. This can only be toggled when `First` subject combine mode, and `WorldSpace Center` resolve mode are selected.", EditCondition = "TargetCombineMode == EViewTargeCombineModes::VTCM_First && TargetResolveType == EViewTargetResolveModes::VTRM_WorldCenter"))
	bool bOffsetInTargetLocalSpace;

	UPROPERTY(EditAnywhere, Interp, Category = "Composition", DisplayName = "Subject Screen Position", meta = (ClampMin = 0.f, ClampMax = 1.f, Tooltip = "Specifies where on the screen you would like the center of the subjects to be. Bottom left is [0,0], and Top right is [1,1], and is independent of screen resolution or aspect ratio."))
	FVector2D ScreenPosition;

	UPROPERTY(EditAnywhere, Interp, Category = "Composition", meta = (ToolTip = "Damping settings for look at motion. Larger values make the component track slowly, and small values more quickly, i.e. 1 is quite slow, and 0.1 is quite fast."))
	FBlackEyeCompositionDamping Damping;

		struct BLACK_EYE_API FBlackEyeCompositionDamping
		{
			UPROPERTY(EditAnywhere, Interp, Category = "", meta = (ToolTip = "When enabled, Yaw damping with control both Pitch and Yaw. Disabled, they can be tuned separately"))
			bool bLinkDamping;

			UPROPERTY(EditAnywhere, Interp, Category = "", DisplayName = "(Yaw) Damping", meta = (ClampMin = 0.f, ToolTip = "Yaw damping rate, or both Yaw and Pitch when \'Link Damping\' is enabled"))
			float YawDamping;

			UPROPERTY(EditAnywhere, Interp, Category = "", meta = (ClampMin = 0.f, ToolTip = "Pitch damping rate", EditCondition = "!bLinkDamping", EditConditionHides))
			float PitchDamping;
		};

	UPROPERTY(EditAnywhere, Interp, Category = "Composition", DisplayName = "Subject Dead Zone", meta = (ClampMin = 0.f, CampMax = 1.f, Tooltip = "Create a screen space area which will ignore all subject motion inside it"))
	FVector2D ScreenDeadZoneSize;
	 */
	
	YAMLOutput += "\n";

	return YAMLOutput;
}

void UBETUtilities::SetLookAtSubjectParams(ULookAtComponent* LookAtComponent, FBlackEyeTarget SubjectParams,
                                           int32 SubjectIndex)
{
	if (!IsValid(LookAtComponent))
	{
		return;
	}

	if (SubjectIndex == 0)
	{
		int NumTargets = LookAtComponent->GetNumTargets();
		for (int i = 0; i < NumTargets; i++)
		{
			FBlackEyeTarget BlackEyeTarget;
			LookAtComponent->GetTargetAtIndex(BlackEyeTarget, i);
			if (!BlackEyeTarget.Actor.IsValid())
			{
				LookAtComponent->SetLookAt(SubjectParams, i, false);
				return;
			}
		}

		return;
	}

	LookAtComponent->SetLookAt(SubjectParams, SubjectIndex - 1, false);
}

void UBETUtilities::SetLookOptions(ULookAtComponent* LookAtComponent, bool bRollRelativeToParent, float Roll, float CameraPlateDistance, float CameraPedestalHeight, const FString& TargetResolveType, float VelocityLookAheadTime, float VelocityLookAheadDamping, bool bLockPositionRelativeToParent, bool bKeepTargetOnScreen)
{
	if (!IsValid(LookAtComponent))
	{
		return;
	}

	// Convert string to enum
	TEnumAsByte<EViewTargetResolveModes> ResolveMode = VTRM_ScreenCenter; // Default value
	if (TargetResolveType == TEXT("Screenspace Center"))
	{
		ResolveMode = VTRM_ScreenCenter;
	}
	else if (TargetResolveType == TEXT("Worldspace Center"))
	{
		ResolveMode = VTRM_WorldCenter;
	}

	// Set all the properties on the LookAtComponent
	LookAtComponent->bRollRelativeToParent = bRollRelativeToParent;
	LookAtComponent->Roll = Roll;
	LookAtComponent->CameraPlateDistance = CameraPlateDistance;
	LookAtComponent->CameraPedestalHeight = CameraPedestalHeight;
	LookAtComponent->TargetResolveType = ResolveMode;
	LookAtComponent->VelocityLookAheadTime = VelocityLookAheadTime;
	LookAtComponent->VelocityLookAheadDamping = VelocityLookAheadDamping;
	LookAtComponent->bLockPositionRelativeToParent = bLockPositionRelativeToParent;
	LookAtComponent->bKeepTargetOnScreen = bKeepTargetOnScreen;
}
