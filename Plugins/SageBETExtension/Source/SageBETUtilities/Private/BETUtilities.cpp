#include "BETUtilities.h"
#include "LogDruids.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonReader.h"
#include "Serialization/JsonSerializer.h"

FString UBETUtilities::GetLookAtSubjectsAsYAML(ULookAtComponent* LookAtComponent)
{
	FString YAMLOutput("LookAtSubjects:");

	if (IsValid(LookAtComponent))
	{
		int NumTargets = LookAtComponent->GetNumTargets();
		for (int i = 0; i < NumTargets; i++)
		{
			FBlackEyeTarget BlackEyeTarget;
			LookAtComponent->GetTargetAtIndex(BlackEyeTarget, i);
			if (BlackEyeTarget.Actor.IsValid())
			{
				YAMLOutput += FString::Printf(TEXT("\n  -SlotIndex:%d\n    UseComponentBounds:%s\n    BoundingRadius:%f\n    Weight:%f\n    ActorName:%s\n    ComponentName:%s\n    BoneName:%s"),
					i + 1,
					(BlackEyeTarget.bAutoSize ? TEXT("true") : TEXT("false")),
					BlackEyeTarget.BoundingRadius,
					BlackEyeTarget.Weight,
					*BlackEyeTarget.Actor->GetActorNameOrLabel(),
					*BlackEyeTarget.ComponentName,
					*BlackEyeTarget.BoneName);
			}
		}
	}

	YAMLOutput += "\n";
	
	return YAMLOutput;
}

FString UBETUtilities::GetLookOptionsAsYAML(ULookAtComponent* LookAtComponent)
{
	FString YAMLOutput("LookOptions:");

	if (IsValid(LookAtComponent))
	{
		// Convert TargetResolveType enum to string
		FString TargetResolveTypeString;
		switch (LookAtComponent->TargetResolveType)
		{
			case VTRM_ScreenCenter:
				TargetResolveTypeString = TEXT("Screenspace Center");
				break;
			case VTRM_WorldCenter:
				TargetResolveTypeString = TEXT("Worldspace Center");
				break;
			default:
				TargetResolveTypeString = TEXT("Unknown");
				break;
		}

		YAMLOutput += FString::Printf(TEXT("\n  - RollRelativeToParent: %s\n    Roll: %f\n    CameraPlateDistance: %f\n    CameraPedestalHeight: %f\n    ViewResolveMode: %s\n    VelocityLookAheadTime: %f\n    VelocityLookAheadDamping: %f\n    LockPositionRelativeToParent: %s\n    KeepTargetOnScreen: %s"),
			(LookAtComponent->bRollRelativeToParent ? TEXT("true") : TEXT("false")),
			LookAtComponent->Roll,
			LookAtComponent->CameraPlateDistance,
			LookAtComponent->CameraPedestalHeight,
			*TargetResolveTypeString,
			LookAtComponent->VelocityLookAheadTime,
			LookAtComponent->VelocityLookAheadDamping,
			(LookAtComponent->bLockPositionRelativeToParent ? TEXT("true") : TEXT("false")),
			(LookAtComponent->bKeepTargetOnScreen ? TEXT("true") : TEXT("false")));
	}

	YAMLOutput += "\n";

	return YAMLOutput;
}

FString UBETUtilities::GetCompositionOptionsAsYAML(ULookAtComponent* LookAtComponent)
{
	FString YAMLOutput("CompositionOptions:");

	if (IsValid(LookAtComponent))
	{
		YAMLOutput += FString::Printf(TEXT("\n  - SubjectOffset: [%f, %f, %f]\n    OffsetInSubjectLocalSpace: %s\n    SubjectScreenPosition: [%f, %f]\n    LinkDamping: %s\n    YawDamping: %f\n    PitchDamping: %f\n    ScreenDeadZoneSize: [%f, %f]"),
			LookAtComponent->TargetOffset.X,
			LookAtComponent->TargetOffset.Y,
			LookAtComponent->TargetOffset.Z,
			(LookAtComponent->bOffsetInTargetLocalSpace ? TEXT("true") : TEXT("false")),
			LookAtComponent->ScreenPosition.X,
			LookAtComponent->ScreenPosition.Y,
			(LookAtComponent->Damping.bLinkDamping ? TEXT("true") : TEXT("false")),
			LookAtComponent->Damping.YawDamping,
			LookAtComponent->Damping.PitchDamping,
			LookAtComponent->ScreenDeadZoneSize.X,
			LookAtComponent->ScreenDeadZoneSize.Y);
	}

	YAMLOutput += "\n";

	return YAMLOutput;
}

FString UBETUtilities::GetLensSettingsAsYAML(ULookAtComponent* LookAtComponent)
{
	FString YAMLOutput("LensSettings:");

/*

 */
	
	YAMLOutput += "\n";

	return YAMLOutput;
}


void UBETUtilities::SetLookAtSubjectParams(ULookAtComponent* LookAtComponent, FBlackEyeTarget SubjectParams,
                                           int32 SubjectIndex)
{
	if (!IsValid(LookAtComponent))
	{
		return;
	}

	if (SubjectIndex == 0)
	{
		int NumTargets = LookAtComponent->GetNumTargets();
		for (int i = 0; i < NumTargets; i++)
		{
			FBlackEyeTarget BlackEyeTarget;
			LookAtComponent->GetTargetAtIndex(BlackEyeTarget, i);
			if (!BlackEyeTarget.Actor.IsValid())
			{
				LookAtComponent->SetLookAt(SubjectParams, i, false);
				return;
			}
		}

		return;
	}

	LookAtComponent->SetLookAt(SubjectParams, SubjectIndex - 1, false);
}

void UBETUtilities::SetLookOptions(ULookAtComponent* LookAtComponent, bool bRollRelativeToParent, float Roll, float CameraPlateDistance, float CameraPedestalHeight, const FString& TargetResolveType, float VelocityLookAheadTime, float VelocityLookAheadDamping, bool bLockPositionRelativeToParent, bool bKeepTargetOnScreen)
{
	if (!IsValid(LookAtComponent))
	{
		return;
	}

	// Convert string to enum
	TEnumAsByte<EViewTargetResolveModes> ResolveMode = VTRM_ScreenCenter; // Default value
	if (TargetResolveType == TEXT("Screenspace Center"))
	{
		ResolveMode = VTRM_ScreenCenter;
	}
	else if (TargetResolveType == TEXT("Worldspace Center"))
	{
		ResolveMode = VTRM_WorldCenter;
	}

	// Set all the properties on the LookAtComponent
	LookAtComponent->bRollRelativeToParent = bRollRelativeToParent;
	LookAtComponent->Roll = Roll;
	LookAtComponent->CameraPlateDistance = CameraPlateDistance;
	LookAtComponent->CameraPedestalHeight = CameraPedestalHeight;
	LookAtComponent->TargetResolveType = ResolveMode;
	LookAtComponent->VelocityLookAheadTime = VelocityLookAheadTime;
	LookAtComponent->VelocityLookAheadDamping = VelocityLookAheadDamping;
	LookAtComponent->bLockPositionRelativeToParent = bLockPositionRelativeToParent;
	LookAtComponent->bKeepTargetOnScreen = bKeepTargetOnScreen;
}

void UBETUtilities::SetCompositionOptions(ULookAtComponent* LookAtComponent, FVector TargetOffset, bool bOffsetInTargetLocalSpace, FVector2D ScreenPosition, bool bLinkDamping, float YawDamping, float PitchDamping, FVector2D ScreenDeadZoneSize)
{
	if (!IsValid(LookAtComponent))
	{
		return;
	}

	// Set all the composition properties on the LookAtComponent
	LookAtComponent->TargetOffset = TargetOffset;
	LookAtComponent->bOffsetInTargetLocalSpace = bOffsetInTargetLocalSpace;
	LookAtComponent->ScreenPosition = ScreenPosition;
	LookAtComponent->Damping.bLinkDamping = bLinkDamping;
	LookAtComponent->Damping.YawDamping = YawDamping;
	LookAtComponent->Damping.PitchDamping = PitchDamping;
	LookAtComponent->ScreenDeadZoneSize = ScreenDeadZoneSize;
}
