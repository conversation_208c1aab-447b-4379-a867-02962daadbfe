#pragma once

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "Engine/World.h"
#include "Engine/DataTable.h"
#include "Black_Eye/Public/Components/LookAtComponent.h"

#include "BETUtilities.generated.h"

struct FBlackEyeTarget;

/**
 * Blueprint function library for BET utilities
 */
UCLASS()
class SAGEBETUTILITIES_API UBETUtilities : public UBlueprintFunctionLibrary
{
    GENERATED_BODY()

public:
    UFUNCTION(BlueprintCallable, Category = "BET Utilities")
    static FString GetLookAtSubjectsAsYAML(ULookAtComponent* LookAtComponent);

    UFUNCTION(BlueprintCallable, Category = "BET Utilities")
    static FString GetLookOptionsAsYAML(ULookAtComponent* LookAtComponent);

    UFUNCTION(BlueprintCallable, Category = "BET Utilities")
    static FString GetCompositionOptionsAsYAML(ULookAtComponent* LookAtComponent);

    UFUNCTION(BlueprintCallable, Category = "BET Utilities")
    static FString GetLensSettingsAsYAML(ULookAtComponent* LookAtComponent);

    UFUNCTION(BlueprintCallable, Category = "BET Utilities")
    static void SetLookAtSubjectParams(ULookAtComponent* LookAtComponent, FBlackEyeTarget SubjectParams, int32 SubjectIndex);

    UFUNCTION(BlueprintCallable, Category = "BET Utilities")
    static void SetLookOptions(ULookAtComponent* LookAtComponent, bool bRollRelativeToParent, float Roll, float CameraPlateDistance, float CameraPedestalHeight, const FString& TargetResolveType, float VelocityLookAheadTime, float VelocityLookAheadDamping, bool bLockPositionRelativeToParent, bool bKeepTargetOnScreen);

    UFUNCTION(BlueprintCallable, Category = "BET Utilities")
    static void SetCompositionOptions(ULookAtComponent* LookAtComponent, FVector TargetOffset, bool bOffsetInTargetLocalSpace, FVector2D ScreenPosition, bool bLinkDamping, float YawDamping, float PitchDamping, FVector2D ScreenDeadZoneSize);

private:
};
