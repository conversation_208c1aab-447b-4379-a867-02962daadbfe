#include "SageExtensionEditorWidget.h"
#include "SageExtensionEditorUtilities.h"
#include "Engine/World.h"
#include "TimerManager.h"
#include "LogDruids.h"

#if WITH_EDITOR
#include "Editor.h"
#endif

USageExtensionEditorWidget::USageExtensionEditorWidget(const FObjectInitializer& ObjectInitializer)
	: Super(ObjectInitializer)
{
}

void USageExtensionEditorWidget::NativeConstruct()
{
	Super::NativeConstruct();

	// Start a timer to periodically check for changes in the active SageExtension
#if WITH_EDITOR
	if (GEditor && GEditor->GetEditorWorldContext().World())
	{
		GEditor->GetEditorWorldContext().World()->GetTimerManager().SetTimer(
			RefreshTimerHandle,
			this,
			&USageExtensionEditorWidget::CheckForActiveExtensionChanges,
			1.0f, // Check every second
			true  // Loop
		);
	}
#endif

	// Initial refresh
	RefreshWidget();
}

void USageExtensionEditorWidget::NativeDestruct()
{
	// Clear the timer
#if WITH_EDITOR
	if (GEditor && GEditor->GetEditorWorldContext().World())
	{
		GEditor->GetEditorWorldContext().World()->GetTimerManager().ClearTimer(RefreshTimerHandle);
	}
#endif

	Super::NativeDestruct();
}

void USageExtensionEditorWidget::RefreshWidget()
{
	USageExtension* CurrentExtension = GetCurrentSageExtension();
	
	if (CurrentExtension != LastActiveSageExtension.Get())
	{
		LastActiveSageExtension = CurrentExtension;
		
		if (CurrentExtension)
		{
			UE_LOG(LogDruidsSage, Log, TEXT("SageExtensionEditorWidget: Active extension changed to %s"), *CurrentExtension->GetClass()->GetName());
			OnSageExtensionActivated(CurrentExtension);
		}
		else
		{
			UE_LOG(LogDruidsSage, Log, TEXT("SageExtensionEditorWidget: No active SageExtension"));
			OnNoSageExtensionActive();
		}
	}

	// Always call the Blueprint event for refresh
	OnRefreshWidget();
}

USageExtension* USageExtensionEditorWidget::GetCurrentSageExtension() const
{
	return USageExtensionEditorUtilities::GetCurrentSageExtension();
}

bool USageExtensionEditorWidget::IsSageExtensionActive() const
{
	return USageExtensionEditorUtilities::IsSageExtensionBlueprintActive();
}

FString USageExtensionEditorWidget::GetCurrentBlueprintName() const
{
	return USageExtensionEditorUtilities::GetCurrentSageExtensionBlueprintName();
}

FDruidsSageExtensionDefinition USageExtensionEditorWidget::GetExtensionDefinition() const
{
	return USageExtensionEditorUtilities::GetCurrentExtensionDefinition();
}

bool USageExtensionEditorWidget::RefreshSageExtension()
{
	bool bSuccess = USageExtensionEditorUtilities::RefreshCurrentSageExtension();
	if (bSuccess)
	{
		// Refresh the widget after refreshing the extension
		RefreshWidget();
	}
	return bSuccess;
}

bool USageExtensionEditorWidget::UpdateExtensionName(const FString& NewName)
{
	bool bSuccess = USageExtensionEditorUtilities::UpdateExtensionName(NewName);
	if (bSuccess)
	{
		RefreshWidget();
	}
	return bSuccess;
}

bool USageExtensionEditorWidget::UpdateExtensionDescription(const FString& NewDescription)
{
	bool bSuccess = USageExtensionEditorUtilities::UpdateExtensionDescription(NewDescription);
	if (bSuccess)
	{
		RefreshWidget();
	}
	return bSuccess;
}

bool USageExtensionEditorWidget::UpdateActionParameterDescription(const FString& ActionFunctionName, const FString& ParameterName, const FString& NewDescription)
{
	bool bSuccess = USageExtensionEditorUtilities::UpdateActionParameterDescription(ActionFunctionName, ParameterName, NewDescription);
	if (bSuccess)
	{
		RefreshWidget();
	}
	return bSuccess;
}

bool USageExtensionEditorWidget::UpdateQueryParameterDescription(const FString& QueryFunctionName, const FString& ParameterName, const FString& NewDescription)
{
	bool bSuccess = USageExtensionEditorUtilities::UpdateQueryParameterDescription(QueryFunctionName, ParameterName, NewDescription);
	if (bSuccess)
	{
		RefreshWidget();
	}
	return bSuccess;
}

void USageExtensionEditorWidget::CheckForActiveExtensionChanges()
{
	// This will trigger a refresh if the active extension has changed
	RefreshWidget();
}
