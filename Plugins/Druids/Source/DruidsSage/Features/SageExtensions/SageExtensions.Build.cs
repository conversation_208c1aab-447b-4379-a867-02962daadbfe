using UnrealBuildTool;

public class SageExtensions : ModuleRules
{
    public SageExtensions(ReadOnlyTargetRules Target) : base(Target)
    {
        PCHUsage = ModuleRules.PCHUsageMode.UseExplicitOrSharedPCHs;

        PublicDependencyModuleNames.AddRange(
            new string[]
            {
                "Core",
                "Blutility",
                "UMG",
            }
        );

        PrivateDependencyModuleNames.AddRange(
            new string[]
            {
                "CoreUObject",
                "Engine",
                "Slate",
                "SlateCore",

                "Json", 
            }
        );

        // Add editor-specific dependencies only when building for editor
        if (Target.bBuildEditor == true)
        {
            PrivateDependencyModuleNames.AddRange(
                new string[]
                {
                    "UnrealEd",
                    "EditorSubsystem",
                    "EditorWidgets",
                    "PropertyEditor",
                    "UMGEditor",
                }
            );
        }
        
        PrivateDependencyModuleNames.AddRange(
            new string[]
            {
                "DruidsCore",

                "SageCommonTypes",
                "Sage<PERSON><PERSON>",

                "SageJSONUtilities",
            }
        );
    }
}